BASE_URL=https://resume.local.com/ai-resume

NEXT_PUBLIC_BASE_URL=https://resume.local.com/ai-resume
NEXT_PUBLIC_TG_URL=https://www.techgig.com
NEXT_PUBLIC_ENGAGE_URL=https://engage.techgig.com

# Google OAuth Configuration
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=

# NextAuth Configuration
NEXTAUTH_SECRET=xFCF/g37FhzzQ/EDKRN/4TfkFoo1g8MLEnJy9E0DGR8=
NEXTAUTH_URL=https://resume.local.com/ai-resume

#OPENAI Configuration
OPENAI_API=https://api.openai.com/v1/chat/completions
OPENAI_MODEL=o4-mini
OPENAI_API_KEY=

SYSTEM_PROMPT='You are a professional resume writer (English Language). Enhance the given description to be more impactful and professional while maintaining truthfulness.'
USER_PROMPT='Please enhance this description to be more professional and impactful: {{job_description}} . important: only use bold(**) and bullet point(-) markdown only where ever necessary and do not use phrases like "here are..." or anything, just give the straight message. strictly under 450 characters.'

# Prisma Connection
DATABASE_URL=mysql://root:root@123@localhost:3306/ai_resume
