'use client'
import { useSession } from 'next-auth/react'
import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { FileText, Mail, User, Trash2, CopyPlus, Loader2, Edit3Icon } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { Toaster } from "@/components/ui/toaster"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

interface Resume {
  id: number
  title?: string
  createdAt: Date
  updatedAt: Date
}

function ProfileSkeleton() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="grid gap-8 md:grid-cols-[300px_1fr]">
        <Skeleton className="h-[300px]" />
        <Skeleton className="h-[500px]" />
      </div>
    </div>
  )
}

export default function Page() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [resumes, setResumes] = useState<Resume[]>([]);
  const [loading, setLoading] = useState(true);
  const [duplicatingId, setDuplicatingId] = useState<number | null>(null);
  const { toast } = useToast();
    const [settings, setSettings] = useState({
    displayName: session?.user?.name || '',
    defaultTemplate: 'modern'
  });

  const deleteResume = async (resumeId: number) => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/resumes/${resumeId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to delete resume');
      }

      toast({
        title: "Success",
        description: "Resume deleted successfully!",
        duration: 3000,
      });

      setResumes((prevResumes) => prevResumes.filter((resume) => resume.id !== resumeId));
    } catch (error) {
      console.error("Error deleting resume:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Error deleting resume. Please try again.",
        variant: "destructive",
        duration: 5000,
      });
    }
  };

  const duplicateResume = async (resumeId: number) => {
    if (duplicatingId) return; // Prevent multiple simultaneous duplications

    try {
      setDuplicatingId(resumeId);

      const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/resumes/${resumeId}/duplicate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to duplicate resume');
      }

      const result = await response.json();

      toast({
        title: "Success",
        description: "Resume duplicated successfully!",
        duration: 3000,
      });

      // Add the new resume to the state
      const newResume: Resume = {
        id: result.resume.id,
        title: result.resume.title || `Resume ${result.resume.id}`,
        createdAt: new Date(result.resume.createdAt),
        updatedAt: new Date(result.resume.updatedAt),
      };

      setResumes((prevResumes) => [newResume, ...prevResumes]);
    } catch (error) {
      console.error("Error duplicating resume:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Error duplicating resume. Please try again.",
        variant: "destructive",
        duration: 5000,
      });
    } finally {
      setDuplicatingId(null);
    }
  };

  useEffect(() => {
    const displayName = window.localStorage.getItem('resumeitnow_name') || session?.user?.name || '';
    const defaultTemplate = window.localStorage.getItem('resumeitnow_template') || 'modern';
    setSettings({ displayName, defaultTemplate });

    const fetchResumes = async () => {
      if (!session?.user?.email) return;

      try {
        const response = await fetch(process.env.NEXT_PUBLIC_BASE_URL+'/api/resumes', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch resumes');
        }

        const result = await response.json();
        const resumeData = result.resumes.map((resume: Resume) => ({
          id: resume.id,
          title: resume.title || `Resume ${resume.id}`,
          createdAt: new Date(resume.createdAt),
          updatedAt: new Date(resume.updatedAt),
        })) as Resume[];

        setResumes(resumeData);
      } catch (error) {
        console.error('Error fetching resumes:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchResumes();
  }, [session?.user?.email, session?.user?.name]);

  if (status === 'loading') {
    return <ProfileSkeleton />;
  }

  if (!session) {
    router.push('/');
    return null;
  }

  return (
    <div className="container min-h-screen mx-auto px-4 py-10">
      <div className="grid gap-8 md:grid-cols-[300px_1fr]">
        {/* Profile Information Card */}
        <Card className="h-fit">
          <CardHeader className="text-center">
            <Avatar className="w-24 h-24 mx-auto mb-4">
              <AvatarImage src={session.user?.image ?? ''} alt={session.user?.name ?? ''} />
              <AvatarFallback>
                {session.user?.name?.charAt(0) ?? 'U'}
              </AvatarFallback>
            </Avatar>
            <CardTitle>{(settings.displayName !== '' ? settings.displayName : session.user?.name)}</CardTitle>
            <CardDescription>
              <div className="flex items-center justify-center gap-2">
                <User className="w-4 h-4" />
                <span>@{session.user?.name ?? 'username'}</span>
              </div>
              <div className="flex items-center justify-center gap-2 mt-2">
                <Mail className="w-4 h-4" />
                <span>{session.user?.email}</span>
              </div>
            </CardDescription>
          </CardHeader>
        </Card>

        {/* Resumes List Card */}
        <Card>
          <CardHeader>
            <CardTitle>My Resumes</CardTitle>
            <CardDescription>Manage your created resumes</CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <Skeleton key={i} className="h-16 w-full" />
                ))}
              </div>
            ) : resumes.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>No resumes found. Create your first resume!</p>
                <Button
                  className="mt-4"
                  onClick={() => router.push('/resume/create')}
                >
                  Create Resume
                </Button>
              </div>
            ) : (
              <div className="grid gap-4">
                {resumes
                  .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
                  .map((resume) => (
                    <Card
                      key={resume.id}
                      className="hover:bg-background transition-colors cursor-pointer"
                    >
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div className="cursor-pointer" >
                            <Edit3Icon
                            <CardTitle className="text-lg" onClick={() => router.push(`/resume/${resume.id}`)}>{resume.title}</CardTitle>
                            <CardDescription>
                              Created: {resume.createdAt.toLocaleDateString()}
                            </CardDescription>
                          </div>
                          <div className="flex items-center gap-2">
                            <FileText className="w-6 h-6 text-muted-foreground" />
                            <button
                              onClick={() => duplicateResume(resume.id)}
                              disabled={duplicatingId === resume.id}
                              className={`transition-colors p-1 rounded ${
                                duplicatingId === resume.id
                                  ? 'text-gray-400 cursor-not-allowed'
                                  : 'text-blue-500 hover:text-blue-700 hover:bg-blue-50'
                              }`}
                              title={duplicatingId === resume.id ? "Duplicating..." : "Duplicate resume"}
                            >
                              {duplicatingId === resume.id ? (
                                <Loader2 size={20} className="animate-spin" />
                              ) : (
                                <CopyPlus size={20} />
                              )}
                            </button>
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <button className="text-red-500 hover:text-red-700 transition-colors">
                                  <Trash2 />
                                </button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    This action cannot be undone. This will permanently delete your resume.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() => deleteResume(resume.id)}
                                    className="bg-red-500 hover:bg-red-600"
                                  >
                                    Delete
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        </div>
                      </CardHeader>
                    </Card>
                  ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
      <Toaster />
    </div>
  );
}