import { NextResponse, type <PERSON>Request } from "next/server";
import puppeteer<PERSON><PERSON>, { type <PERSON>rowser as Browser<PERSON><PERSON> } from 'puppeteer-core';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/authOptions';
import { generateTempToken } from '@/lib/temp-token-utils';
import { validatePermanentToken } from '@/lib/permanent-token-utils';
import { PrismaClient } from '@/lib/generated/prisma';

// The path where Chromium is installed via 'apk' in the Alpine Docker image
const CHROMIUM_PATH = '/usr/bin/chromium-browser';

const prisma = new PrismaClient();

export const dynamic = 'force-dynamic';
export const maxDuration = 60;

export async function GET(request: NextRequest) {
    const { searchParams } = new URL(request.url);
    const resumeId = searchParams.get('resume_id');
    const token = searchParams.get('token');
    console.log('Search Params:', searchParams.toString());

    let userId: number;
    let userEmail: string;

    // Check if permanent token is provided
    if (token) {
        // Authenticate using permanent token
        const tokenPayload = await validatePermanentToken(token);
        if (!tokenPayload) {
            return NextResponse.json({
                message: 'Invalid or expired token'
            }, { status: 401 });
        }

        if (!resumeId) {
            return NextResponse.json({
                message: 'Resume ID is required'
            }, { status: 400 });
        }

        const resumeIdNum = parseInt(resumeId);
        if (isNaN(resumeIdNum)) {
            return NextResponse.json({
                message: 'Invalid resume ID'
            }, { status: 400 });
        }

        // Verify that the resume belongs to the authenticated user
        const resume = await prisma.resume.findFirst({
            where: {
                id: resumeIdNum,
                // user: { email: userEmail }
            }
        });

        if (!resume) {
            return NextResponse.json({
                message: 'Resume not found or access denied in api pmtkn'
            }, { status: 404 });
        }

        // For permanent token authentication, pass the token directly to the download page
        // No need to generate temporary tokens
        searchParams.append('token', token);
        searchParams.append('template', resume.template);
        if (resume.accentColor) searchParams.append('accentColor', resume.accentColor);
        if (resume.fontFamily) searchParams.append('fontFamily', resume.fontFamily);
        if (resume.sectionOrder) searchParams.append('sectionOrder', JSON.stringify(resume.sectionOrder));

    } else {
        // Fall back to NextAuth session authentication
        const session = await getServerSession(authOptions);

        if (!session?.user?.id || !session?.user?.email) {
            return NextResponse.json({
                message: 'User not authenticated'
            }, { status: 401 });
        }

        userId = session.user.id;
        userEmail = session.user.email;

        if (!resumeId) {
            return NextResponse.json({
                message: 'Resume ID is required'
            }, { status: 400 });
        }

        const resumeIdNum = parseInt(resumeId);
        if (isNaN(resumeIdNum)) {
            return NextResponse.json({
                message: 'Invalid resume ID'
            }, { status: 400 });
        }

        // Verify that the resume belongs to the authenticated user
        const resume = await prisma.resume.findFirst({
            where: {
                id: resumeIdNum,
                user: { email: userEmail }
            }
        });

        if (!resume) {
            return NextResponse.json({
                message: 'Resume not found or access denied api tmtkn'
            }, { status: 404 });
        }

        // Generate temporary token for PDF generation
        const tempToken = generateTempToken(userId, resumeIdNum);

        // Add the temporary token and template info to search params
        searchParams.append('userId', userId.toString());
        searchParams.append('tempToken', tempToken);
        searchParams.append('template', resume.template);
        if (resume.accentColor) searchParams.append('accentColor', resume.accentColor);
        if (resume.fontFamily) searchParams.append('fontFamily', resume.fontFamily);
        if (resume.sectionOrder) searchParams.append('sectionOrder', JSON.stringify(resume.sectionOrder));


    }


    if (!searchParams.toString()) {
        return NextResponse.json({
            message: 'No query parameters provided'
        }, { status: 400 });
    }

    let browser: BrowserCore | null = null;
    try {
        // Launch Puppeteer using the system-installed Chromium
        browser = await puppeteerCore.launch({
            executablePath: CHROMIUM_PATH,
            // These args are crucial for running in a Docker container
            args: [
            	'--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage', // Recommended for Docker
                '--single-process', // May improve performance in some container environments
                '--disable-gpu', // Often recommended in headless environments
                '--disable-software-rasterizer', // Can help with rendering issues
	    ],
            headless: true,
        });

        const page = await browser.newPage();
        
        // Construct URL using the exact same query parameters
        const baseUrl = process.env.BASE_URL;
        const url = new URL(`${baseUrl}/resume/download`);
        url.search = searchParams.toString();
        console.log('Navigating to Download URL:', url.toString());

        await page.goto(url.toString(), { waitUntil: 'networkidle2',timeout:30000 });
        
        // Wait for the resume-content div to appear
        await page.waitForSelector('#resume-content', { 
            visible: true,
            timeout: 30000
        });

        const pdf = await page.pdf({
            format: 'A4',
            printBackground: true,
            margin: {
                top: '20px',
                right: '10px',
                bottom: '20px',
                left: '10px',
            },
        });

        return new NextResponse(pdf as BodyInit, {
            status: 200,
            headers: {
                'Content-Type': 'application/pdf',
                'Content-Disposition': `attachment; filename=resume.pdf`,
            },
        });
    } catch (error) {
        console.error('PDF generation error:', error);
        return NextResponse.json(
            { message: 'Error generating PDF :'+error },
            { status: 500 }
        );
    } finally {
        // Ensure the browser is closed even if an error occurs
        if (browser) {
            await browser.close();
        }
    }
}
