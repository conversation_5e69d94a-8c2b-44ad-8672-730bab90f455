import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/authOptions';
import { 
  createPermanentToken, 
  getUserPermanentTokens, 
  deactivatePermanentToken,
  deletePermanentToken,
  checkTokenLimit 
} from '@/lib/permanent-token-utils';

export const dynamic = 'force-dynamic';

/**
 * GET /api/permanent-tokens - Get all permanent tokens for the authenticated user
 */
export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    const tokens = await getUserPermanentTokens(session.user.id);
    
    return NextResponse.json({
      tokens: tokens.map(token => ({
        id: token.id,
        name: token.name,
        expiresAt: token.expiresAt,
        isActive: token.isActive,
        createdAt: token.createdAt
        // Note: We don't return the actual token value for security
      }))
    });
  } catch (error) {
    console.error('Error fetching permanent tokens:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/permanent-tokens - Create a new permanent token
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { name, expiresAt } = body;

    // Check token limit
    const canCreateToken = await checkTokenLimit(session.user.id, 10);
    if (!canCreateToken) {
      return NextResponse.json(
        { error: 'Maximum number of tokens reached' },
        { status: 400 }
      );
    }

    // Parse expiry date if provided
    let expiryDate: Date | undefined;
    if (expiresAt) {
      expiryDate = new Date(expiresAt);
      if (isNaN(expiryDate.getTime())) {
        return NextResponse.json(
          { error: 'Invalid expiry date' },
          { status: 400 }
        );
      }
      
      // Check if expiry date is in the future
      if (expiryDate <= new Date()) {
        return NextResponse.json(
          { error: 'Expiry date must be in the future' },
          { status: 400 }
        );
      }
    }

    const result = await createPermanentToken(session.user.id, name, expiryDate);
    
    return NextResponse.json({
      message: 'Token created successfully',
      token: result.token, // Only time the token is returned
      tokenInfo: {
        id: result.tokenRecord.id,
        name: result.tokenRecord.name,
        expiresAt: result.tokenRecord.expiresAt,
        isActive: result.tokenRecord.isActive,
        createdAt: result.tokenRecord.createdAt
      }
    });
  } catch (error) {
    console.error('Error creating permanent token:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/permanent-tokens - Delete or deactivate a permanent token
 */
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const tokenId = searchParams.get('id');
    const action = searchParams.get('action') || 'deactivate'; // 'deactivate' or 'delete'

    if (!tokenId) {
      return NextResponse.json(
        { error: 'Token ID is required' },
        { status: 400 }
      );
    }

    const tokenIdNum = parseInt(tokenId);
    if (isNaN(tokenIdNum)) {
      return NextResponse.json(
        { error: 'Invalid token ID' },
        { status: 400 }
      );
    }

    let success = false;
    if (action === 'delete') {
      success = await deletePermanentToken(tokenIdNum, session.user.id);
    } else {
      success = await deactivatePermanentToken(tokenIdNum, session.user.id);
    }

    if (!success) {
      return NextResponse.json(
        { error: 'Token not found or access denied' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: `Token ${action}d successfully`
    });
  } catch (error) {
    const { searchParams } = new URL(request.url);
    const errorAction = searchParams.get('action') || 'deactivate';
    console.error(`Error ${errorAction}ing permanent token:`, error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
