import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/authOptions';
import { duplicateResume, convertToResumeData } from '@/lib/resume-utils';

/**
 * POST /api/resumes/[resumeId]/duplicate - Duplicate a specific resume
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { resumeId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    const resumeId = parseInt(params.resumeId);
    if (isNaN(resumeId)) {
      return NextResponse.json(
        { error: 'Invalid resume ID' },
        { status: 400 }
      );
    }

    const duplicatedResume = await duplicateResume(resumeId, session.user.email);

    if (!duplicatedResume) {
      return NextResponse.json(
        { error: 'Resume not found or access denied' },
        { status: 404 }
      );
    }

    // Convert database format to expected ResumeData format
    const resumeWithContent = {
      ...duplicatedResume,
      content: convertToResumeData(duplicatedResume)
    };

    return NextResponse.json({
      success: true,
      resume: resumeWithContent,
      message: 'Resume duplicated successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Error duplicating resume:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
