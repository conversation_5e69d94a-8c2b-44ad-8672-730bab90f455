import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/authOptions';
import { PrismaClient } from '@/lib/generated/prisma';

const prisma = new PrismaClient();
export const dynamic = 'force-dynamic';

/**
 * GET /api/user/settings - Get user settings
 */
export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: {
        id: true,
        name: true,
        email: true,
        default_template: true,
      }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      settings: {
        displayName: user.name || '',
        defaultTemplate: user.default_template || 'modern'
      }
    });

  } catch (error) {
    console.error('Error fetching user settings:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/user/settings - Update user settings
 */
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { displayName, defaultTemplate } = body;

    // Validate input
    if (typeof displayName !== 'string' || typeof defaultTemplate !== 'string') {
      return NextResponse.json(
        { error: 'Invalid input data' },
        { status: 400 }
      );
    }

    // Validate template exists
    const validTemplates = ['modern', 'modern_old', 'minimal', 'professional'];
    if (!validTemplates.includes(defaultTemplate)) {
      return NextResponse.json(
        { error: 'Invalid template selection' },
        { status: 400 }
      );
    }

    // Update user settings
    const updatedUser = await prisma.user.update({
      where: { email: session.user.email },
      data: {
        name: displayName.trim() || null,
        default_template: defaultTemplate,
        updatedAt: new Date()
      },
      select: {
        id: true,
        name: true,
        email: true,
        default_template: true,
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Settings updated successfully',
      settings: {
        displayName: updatedUser.name || '',
        defaultTemplate: updatedUser.default_template
      }
    });

  } catch (error) {
    console.error('Error updating user settings:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
