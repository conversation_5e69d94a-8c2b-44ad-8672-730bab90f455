"use client";
import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { ModernTemplate } from '@/components/resume/templates/Modern';
import { MinimalTemplate } from '@/components/resume/templates/Minimal';
import { ProfessionalTemplate } from '@/components/resume/templates/Professional';
import { OldModernTemplate } from '@/components/resume/templates/Modern-old';
import { ResumeData } from '@/lib/types/resume';


const TEMPLATES = {
  modern: ModernTemplate,
  modern_old: OldModernTemplate,
  minimal: MinimalTemplate,
  professional: ProfessionalTemplate,
  
} as const;

type TemplateKey = keyof typeof TEMPLATES;



const DownloadPage = () => {
  const searchParams = useSearchParams();
  const [resumeData, setResumeData] = useState<ResumeData | null>(null); // eslint-disable-line @typescript-eslint/no-explicit-any
  const [selectedTemplate, setSelectedTemplate] = useState<TemplateKey | null>(null);
  const [accentColor, setAccentColor] = useState<string | undefined>(undefined);
  const [fontFamily, setFontFamily] = useState<string | undefined>(undefined);
  const [sectionOrder, setSectionOrder] = useState<string[] | undefined>(undefined);

  async function fetchResumeData(resumeId: string, token?: string): Promise<ResumeData | null> {
    try {
      // Use token endpoint if token is available (supports both temporary and permanent tokens),
      // otherwise use regular endpoint
      const endpoint = token
        ? `${process.env.NEXT_PUBLIC_BASE_URL}/api/resumes/${resumeId}/temp?token=${token}`
        : `${process.env.NEXT_PUBLIC_BASE_URL}/api/resumes/${resumeId}`;

        console.log('ENDPOINT::::', endpoint);
      const response = await fetch(endpoint, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        console.error('Failed to fetch resume:', response.status, response.statusText);
        return null;
      }

      const result = await response.json();
      return result.resume.content as ResumeData;
    } catch (error) {
      console.error('Error fetching resume:', error);
      return null;
    }
  }

  useEffect(() => {
    const resumeId = searchParams.get('resume_id');
    const template = searchParams.get('template');
    const color = searchParams.get('accentColor');
    const font = searchParams.get('fontFamily');
    const order = searchParams.get('sectionOrder');
    const userId = searchParams.get('userId');
    const tempToken = searchParams.get('tempToken');
    const permanentToken = searchParams.get('token');

    // Support both authentication methods:
    // 1. Temporary token (requires userId, tempToken, and template)
    // 2. Permanent token (requires token and template)
    const hasValidTempAuth = userId && tempToken && resumeId && template && template in TEMPLATES;
    const hasValidPermanentAuth = permanentToken && resumeId && template && template in TEMPLATES;
    console.log('hasValidTempAuth', hasValidTempAuth);
    console.log('hasValidPermanentAuth', hasValidPermanentAuth);

    if (hasValidTempAuth || hasValidPermanentAuth) {
      try {
        // Use the appropriate token (permanent token takes precedence)
        const tokenToUse = permanentToken || tempToken;
        console.log('tokenToUse', tokenToUse);

        fetchResumeData(resumeId, tokenToUse || undefined).then((data) => {
          if (data) {
            setResumeData(data);
          } else {
            console.error('Failed to fetch resume data');
          }
        });
        setSelectedTemplate(template as TemplateKey);
        setAccentColor(color || undefined); // Use undefined to fall back to template default
        setFontFamily(font || undefined);   // Use undefined to fall back to template default
        if (order) {
          setSectionOrder(JSON.parse(order)); // Expecting a JSON string like `["education", "skills", ...]`
        } else {
          setSectionOrder(undefined); // Use undefined to fall back to template default
        }
      } catch (error) {
        console.error('Error parsing query parameters:', error);
      }
    } else {
      console.error('Invalid authentication parameters. Need either (userId + tempToken) or (token) with resumeId and template');
    }
  }, [searchParams]);

  if (!resumeData || !selectedTemplate) {
    return <div>Loading...</div>;
  }
  
  const TemplateComponent = TEMPLATES[selectedTemplate];

  return (
    <div className="">
      <div id="resume-content">
        <TemplateComponent
          resumeData={resumeData}
          isEditing={false}
          updateField={() => {}}
          accentColor={accentColor}    // Pass accentColor from query params
          fontFamily={fontFamily}      // Pass fontFamily from query params
          sectionOrder={sectionOrder}  // Pass sectionOrder from query params
        />
      </div>
    </div>
  );
};

export default DownloadPage;