# ---------- Stage 1: Build ----------
# Use an Alpine-based Node.js image for the build stage
FROM node:20-alpine AS builder

# Set the Puppeteer environment variable to skip downloading Chromium during 'npm install'
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true

WORKDIR /app

# Copy package files first for better caching
COPY package.json package-lock.json* ./

# Install dependencies
RUN npm install

# Copy the rest of your app's source code (includes prisma directory)
COPY . .

COPY prisma ./prisma

# Generate Prisma client (this creates ./lib/generated/prisma)
RUN npx prisma generate

# Set production environment for the build
ENV NEXT_TELEMETRY_DISABLED 1
ENV NODE_ENV=production

# Build your Next.js app
RUN npm run build

# ---------- Stage 2: Run ----------
# Use the same Alpine base for the final, lean runner stage
FROM node:20-alpine AS runner

WORKDIR /app

# Set production environment for running the app
ENV NODE_ENV=production
ENV PORT=8888
ENV NEXT_TELEMETRY_DISABLED 1

# 1. Install ONLY the RUNTIME dependencies.
# - Use 'apk' which is the package manager for Alpine Linux.
# - We need chromium and the necessary font packages for it to render text correctly.
# - '--no-cache' keeps the image size down by not storing the package index.
RUN apk add --no-cache \
    chromium \
    nss_wrapper \
    freetype \
    harfbuzz \
    ca-certificates \
    ttf-freefont


# Copy everything needed to run and migrate
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/public ./public
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/next.config.mjs ./next.config.mjs
COPY --from=builder /app/tsconfig.json ./tsconfig.json

# Copy Prisma schema and migrations
COPY --from=builder /app/prisma/ ./prisma/

# Copy generated Prisma client and other lib files
COPY --from=builder /app/lib/ ./lib/

# Apply Prisma migrations before starting the app
CMD ["sh", "-c", "npm run db:deploy && npm start"]
