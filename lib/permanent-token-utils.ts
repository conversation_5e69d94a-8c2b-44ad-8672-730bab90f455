/**
 * Permanent Token Utilities
 * For secure external API access authentication
 */

import { PrismaClient } from '@/lib/generated/prisma';
import { randomBytes, createHash } from 'crypto';

const prisma = new PrismaClient();

export interface PermanentTokenPayload {
  id: number;
  userId: number;
  name?: string;
  expiresAt?: Date;
  isActive: boolean;
  createdAt: Date;
}

/**
 * Generate a secure random token
 */
function generateSecureToken(): string {
  // Generate 32 random bytes and convert to hex (64 characters)
  return randomBytes(32).toString('hex');
}

/**
 * Hash a token for secure storage
 */
function hashToken(token: string): string {
  return createHash('sha256').update(token).digest('hex');
}

/**
 * Create a new permanent token for a user
 */
export async function createPermanentToken(
  userId: number,
  name?: string,
  expiresAt?: Date
): Promise<{ token: string; tokenRecord: PermanentTokenPayload }> {
  try {
    // Generate a secure token
    const token = generateSecureToken();
    const hashedToken = hashToken(token);

    // Create the token record in the database
    const tokenRecord = await prisma.permanentToken.create({
      data: {
        token: hashedToken,
        user_id: userId,
        name: name || null,
        expiresAt: expiresAt || null,
        isActive: true
      }
    });

    console.log('Permanent token created successfully:', tokenRecord.id);
    
    return {
      token, // Return the unhashed token to the user (only time they'll see it)
      tokenRecord: {
        id: tokenRecord.id,
        userId: tokenRecord.user_id,
        name: tokenRecord.name || undefined,
        expiresAt: tokenRecord.expiresAt || undefined,
        isActive: tokenRecord.isActive,
        createdAt: tokenRecord.createdAt
      }
    };
  } catch (error) {
    console.error('Error creating permanent token:', error);
    throw error;
  }
}

/**
 * Validate a permanent token and return user information
 */
export async function validatePermanentToken(token: string): Promise<PermanentTokenPayload | null> {
  try {
    const hashedToken = hashToken(token);

    // Find the token in the database
    const tokenRecord = await prisma.permanentToken.findUnique({
      where: { token: hashedToken },
      include: { user: true }
    });

    if (!tokenRecord) {
      return null;
    }

    // Check if token is active
    if (!tokenRecord.isActive) {
      console.log('Token is inactive:', tokenRecord.id);
      return null;
    }

    // Check if token has expired
    if (tokenRecord.expiresAt && tokenRecord.expiresAt < new Date()) {
      console.log('Token has expired:', tokenRecord.id);
      return null;
    }

    return {
      id: tokenRecord.id,
      userId: tokenRecord.user_id,
      name: tokenRecord.name || undefined,
      expiresAt: tokenRecord.expiresAt || undefined,
      isActive: tokenRecord.isActive,
      createdAt: tokenRecord.createdAt
    };
  } catch (error) {
    console.error('Error validating permanent token:', error);
    return null;
  }
}

/**
 * Get all permanent tokens for a user
 */
export async function getUserPermanentTokens(userId: number): Promise<PermanentTokenPayload[]> {
  try {
    const tokens = await prisma.permanentToken.findMany({
      where: { user_id: userId },
      orderBy: { createdAt: 'desc' }
    });

    return tokens.map(token => ({
      id: token.id,
      userId: token.user_id,
      name: token.name || undefined,
      expiresAt: token.expiresAt || undefined,
      isActive: token.isActive,
      createdAt: token.createdAt
    }));
  } catch (error) {
    console.error('Error getting user permanent tokens:', error);
    return [];
  }
}

/**
 * Deactivate a permanent token
 */
export async function deactivatePermanentToken(tokenId: number, userId: number): Promise<boolean> {
  try {
    const result = await prisma.permanentToken.updateMany({
      where: {
        id: tokenId,
        user_id: userId // Ensure user can only deactivate their own tokens
      },
      data: { isActive: false }
    });

    return result.count > 0;
  } catch (error) {
    console.error('Error deactivating permanent token:', error);
    return false;
  }
}

/**
 * Delete a permanent token
 */
export async function deletePermanentToken(tokenId: number, userId: number): Promise<boolean> {
  try {
    const result = await prisma.permanentToken.deleteMany({
      where: {
        id: tokenId,
        user_id: userId // Ensure user can only delete their own tokens
      }
    });

    return result.count > 0;
  } catch (error) {
    console.error('Error deleting permanent token:', error);
    return false;
  }
}

/**
 * Update a permanent token's name or expiry
 */
export async function updatePermanentToken(
  tokenId: number,
  userId: number,
  updates: { name?: string; expiresAt?: Date | null }
): Promise<boolean> {
  try {
    const result = await prisma.permanentToken.updateMany({
      where: {
        id: tokenId,
        user_id: userId // Ensure user can only update their own tokens
      },
      data: {
        ...(updates.name !== undefined && { name: updates.name }),
        ...(updates.expiresAt !== undefined && { expiresAt: updates.expiresAt })
      }
    });

    return result.count > 0;
  } catch (error) {
    console.error('Error updating permanent token:', error);
    return false;
  }
}

/**
 * Check if a user has reached the maximum number of tokens (optional rate limiting)
 */
export async function checkTokenLimit(userId: number, maxTokens: number = 10): Promise<boolean> {
  try {
    const activeTokenCount = await prisma.permanentToken.count({
      where: {
        user_id: userId,
        isActive: true
      }
    });

    return activeTokenCount < maxTokens;
  } catch (error) {
    console.error('Error checking token limit:', error);
    return false;
  }
}
