/**
 * Resume Utilities
 * Functions for managing resume CRUD operations with MySQL/Prisma
 */

import { PrismaClient } from '@/lib/generated/prisma';
import { CreateResumeInput, UpdateResumeInput, ResumeRecord, ResumeData } from '@/lib/types/resume';

const prisma = new PrismaClient();

/**
 * Helper function to convert database record to ResumeData format
 */
export function convertToResumeData(resume: ResumeRecord): ResumeData {
  return {
    personalDetails: {
      fullName: resume.Name || '',
      email: resume.Email || '',
      phone: resume.Phone || '',
      linkedin: resume.LinkedIn || '',
      github: resume.GitHub || '',
      location: resume.Location || ''
    },
    objective: resume.Objective || '',
    jobTitle: resume.JobTitle || '',
    workExperience: resume.WorkExperience || [],
    education: resume.Education || [],
    skills: resume.Skills || [],
    projects: resume.Projects || [],
    languages: resume.Languages || [],
    certifications: resume.Certifications || [],
    customSections: resume.CustomSections || [],
    accentColor: resume.accentColor,
    fontFamily: resume.fontFamily,
    sectionOrder: resume.sectionOrder
  };
}

/**
 * Create a new resume for a user
 */
export async function createResume(userEmail: string, resumeInput: CreateResumeInput): Promise<ResumeRecord> {
  try {
    // First, get the user by email
    const user = await prisma.user.findUnique({
      where: { email: userEmail }
    });

    if (!user) {
      throw new Error(`User not found for email: ${userEmail}`);
    }

    // Create the resume
    const { content } = resumeInput;
    const resume = await prisma.resume.create({
      data: {
        title: resumeInput.title,
        user_id: user.id,
        Name: content.personalDetails?.fullName || null,
        Email: content.personalDetails?.email || null,
        Phone: content.personalDetails?.phone || null,
        LinkedIn: content.personalDetails?.linkedin || null,
        GitHub: content.personalDetails?.github || null,
        Location: content.personalDetails?.location || null,
        Objective: content.objective || null,
        JobTitle: content.jobTitle || null,
        WorkExperience: content.workExperience as object || null,
        Education: content.education as object || null,
        Skills: content.skills as object || null,
        Projects: content.projects as object || null,
        Certifications: content.certifications as object || null,
        Languages: content.languages as object || null,
        CustomSections: content.customSections as object || null,
        template: resumeInput.template || 'modern',
        accentColor: resumeInput.accentColor,
        fontFamily: resumeInput.fontFamily,
        sectionOrder: resumeInput.sectionOrder as object, // Prisma Json type
      }
    });

    // Update user's resume count
    await prisma.user.update({
      where: { id: user.id },
      data: { resumes_count: { increment: 1 } }
    });

    console.log('Resume created successfully:', resume.id);
    return resume as ResumeRecord;
  } catch (error) {
    console.error('Error creating resume:', error);
    throw error;
  }
}

/**
 * Get a specific resume by ID and user email
 */
export async function getResumeById(resumeId: number, userEmail?: string): Promise<ResumeRecord | null> {
  try {
    const resume = await prisma.resume.findFirst({
      where: {
        id: resumeId,
        ...(userEmail && { user: { email: userEmail } }) // only add if userEmail exists
      },
      include: {
        user: true
      }
    });

    return resume as ResumeRecord;
  } catch (error) {
    console.error('Error getting resume by ID:', error);
    return null;
  }
}

/**
 * Get all resumes for a user
 */
export async function getUserResumes(userEmail: string): Promise<ResumeRecord[]> {
  try {
    const resumes = await prisma.resume.findMany({
      where: {
        user: { email: userEmail }
      },
      orderBy: { updatedAt: 'desc' }
    });

    return resumes as ResumeRecord[];
  } catch (error) {
    console.error('Error getting user resumes:', error);
    return [];
  }
}

/**
 * Update an existing resume
 */
export async function updateResume(
  resumeId: number, 
  userEmail: string, 
  updateInput: UpdateResumeInput
): Promise<ResumeRecord | null> {
  try {
    // First verify the resume belongs to the user
    const existingResume = await getResumeById(resumeId, userEmail);
    if (!existingResume) {
      throw new Error('Resume not found or access denied');
    }

    // Update the resume
    const updateData: Record<string, unknown> = {
      title: updateInput.title,
      template: updateInput.template,
      accentColor: updateInput.accentColor,
      fontFamily: updateInput.fontFamily,
      sectionOrder: updateInput.sectionOrder as object,
    };

    // If content is provided, update individual fields
    if (updateInput.content) {
      const { content } = updateInput;
      updateData.Name = content.personalDetails?.fullName || null;
      updateData.Email = content.personalDetails?.email || null;
      updateData.Phone = content.personalDetails?.phone || null;
      updateData.LinkedIn = content.personalDetails?.linkedin || null;
      updateData.GitHub = content.personalDetails?.github || null;
      updateData.Location = content.personalDetails?.location || null;
      updateData.Objective = content.objective || null;
      updateData.JobTitle = content.jobTitle || null;
      updateData.WorkExperience = content.workExperience as object || null;
      updateData.Education = content.education as object || null;
      updateData.Skills = content.skills as object || null;
      updateData.Projects = content.projects as object || null;
      updateData.Certifications = content.certifications as object || null;
      updateData.Languages = content.languages as object || null;
      updateData.CustomSections = content.customSections as object || null;
    }

    const updatedResume = await prisma.resume.update({
      where: { id: resumeId },
      data: updateData
    });

    console.log('Resume updated successfully:', resumeId);
    return updatedResume as ResumeRecord;
  } catch (error) {
    console.error('Error updating resume:', error);
    throw error;
  }
}

/**
 * Delete a resume
 */
export async function deleteResume(resumeId: number, userEmail: string): Promise<boolean> {
  try {
    // First verify the resume belongs to the user
    const existingResume = await getResumeById(resumeId, userEmail);
    if (!existingResume) {
      throw new Error('Resume not found or access denied');
    }

    // Delete the resume
    await prisma.resume.delete({
      where: { id: resumeId }
    });

    // Update user's resume count
    const user = await prisma.user.findUnique({
      where: { email: userEmail }
    });

    if (user) {
      await prisma.user.update({
        where: { id: user.id },
        data: { resumes_count: { decrement: 1 } }
      });
    }

    console.log('Resume deleted successfully:', resumeId);
    return true;
  } catch (error) {
    console.error('Error deleting resume:', error);
    throw error;
  }
}

/**
 * Check if a resume belongs to a user
 */
export async function checkResumeOwnership(resumeId: number, userEmail: string): Promise<boolean> {
  try {
    const resume = await prisma.resume.findFirst({
      where: {
        id: resumeId,
        user: { email: userEmail }
      }
    });

    return !!resume;
  } catch (error) {
    console.error('Error checking resume ownership:', error);
    return false;
  }
}

/**
 * Get resume count for a user
 */
export async function getUserResumeCount(userEmail: string): Promise<number> {
  try {
    const count = await prisma.resume.count({
      where: {
        user: { email: userEmail }
      }
    });

    return count;
  } catch (error) {
    console.error('Error getting resume count:', error);
    return 0;
  }
}

/**
 * Duplicate an existing resume
 */
export async function duplicateResume(resumeId: number, userEmail: string): Promise<ResumeRecord | null> {
  try {
    // First verify the resume belongs to the user and get the original resume
    const originalResume = await getResumeById(resumeId, userEmail);
    if (!originalResume) {
      throw new Error('Resume not found or access denied');
    }

    // Get the user by email
    const user = await prisma.user.findUnique({
      where: { email: userEmail }
    });

    if (!user) {
      throw new Error(`User not found for email: ${userEmail}`);
    }

    // Create a new title for the duplicated resume
    const originalTitle = originalResume.title || `Resume ${originalResume.id}`;
    const duplicatedTitle = `${originalTitle} (Copy)`;

    // Create the duplicated resume with all the same data except id, title, and timestamps
    const duplicatedResume = await prisma.resume.create({
      data: {
        title: duplicatedTitle,
        user_id: user.id,
        Name: originalResume.Name,
        Email: originalResume.Email,
        Phone: originalResume.Phone,
        LinkedIn: originalResume.LinkedIn,
        GitHub: originalResume.GitHub,
        Location: originalResume.Location,
        Objective: originalResume.Objective,
        JobTitle: originalResume.JobTitle,
        WorkExperience: originalResume.WorkExperience as object || null,
        Education: originalResume.Education as object || null,
        Skills: originalResume.Skills as object || null,
        Projects: originalResume.Projects as object || null,
        Certifications: originalResume.Certifications as object || null,
        Languages: originalResume.Languages as object || null,
        CustomSections: originalResume.CustomSections as object || null,
        template: originalResume.template,
        accentColor: originalResume.accentColor,
        fontFamily: originalResume.fontFamily,
        sectionOrder: originalResume.sectionOrder as object,
      }
    });

    // Update user's resume count
    await prisma.user.update({
      where: { id: user.id },
      data: { resumes_count: { increment: 1 } }
    });

    console.log('Resume duplicated successfully:', duplicatedResume.id);
    return duplicatedResume as ResumeRecord;
  } catch (error) {
    console.error('Error duplicating resume:', error);
    throw error;
  }
}
