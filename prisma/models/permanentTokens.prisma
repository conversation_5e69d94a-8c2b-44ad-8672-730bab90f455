model PermanentToken {
  id Int @id @default(autoincrement())
  token String @unique
  user_id Int
  user User @relation(fields: [user_id], references: [id], onDelete: Cascade)
  name String? // Optional name/description for the token
  expiresAt DateTime? // Optional expiry date, null means no expiry
  isActive Boolean @default(true) // Allow tokens to be disabled without deletion
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("PermanentToken")
}
