model User{
  id Int @id @default(autoincrement())
  email String @unique
  name String?
  source Source @default(Google)
  resumes_count Int @default(0)
  default_template String @default("modern")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  utmTracking UTMTracking[]
  resumes Resume[]
  permanentTokens PermanentToken[]

  @@map("User")
}

enum Source {
  Google
  LinkedIn
}