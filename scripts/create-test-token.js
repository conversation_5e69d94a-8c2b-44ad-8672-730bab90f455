/**
 * Test script to create a permanent token for testing
 * Run with: node scripts/create-test-token.js
 */

const { PrismaClient } = require('../lib/generated/prisma');
const { createPermanentToken } = require('../lib/permanent-token-utils');

async function createTestToken() {
  const prisma = new PrismaClient();
  
  try {
    // Get the first user from the database
    const user = await prisma.user.findFirst();
    
    if (!user) {
      console.error('No users found in the database. Please create a user first.');
      return;
    }
    
    console.log(`Creating test token for user: ${user.email} (ID: ${user.id})`);
    
    // Create a permanent token that expires in 1 year
    const expiryDate = new Date();
    expiryDate.setFullYear(expiryDate.getFullYear() + 1);
    
    const result = await createPermanentToken(
      user.id,
      'Test Token - Created by Script',
      expiryDate
    );
    
    console.log('\n✅ Token created successfully!');
    console.log('Token:', result.token);
    console.log('Token ID:', result.tokenRecord.id);
    console.log('Expires:', result.tokenRecord.expiresAt);
    
    console.log('\n📝 Test the token with:');
    console.log(`curl -X GET "http://localhost:8888/ai-resume/api/pdf?resume_id=1&token=${result.token}" -o test-resume.pdf`);
    
    // Get user's resumes for testing
    const resumes = await prisma.resume.findMany({
      where: { user_id: user.id },
      select: { id: true, title: true }
    });
    
    if (resumes.length > 0) {
      console.log('\n📋 Available resumes for testing:');
      resumes.forEach(resume => {
        console.log(`- Resume ID: ${resume.id}, Title: ${resume.title || 'Untitled'}`);
      });
    } else {
      console.log('\n⚠️  No resumes found for this user. Create a resume first.');
    }
    
  } catch (error) {
    console.error('Error creating test token:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestToken();
