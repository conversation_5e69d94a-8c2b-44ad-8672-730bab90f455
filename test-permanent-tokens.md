# Testing Permanent Token Authentication for PDF API

This document outlines the test scenarios for the new permanent token authentication system.

## Prerequisites

1. Ensure the database migration has been applied
2. Have a user account with at least one resume
3. Have the application running locally or in development environment

## Test Scenarios

### 1. Create a Permanent Token

**Endpoint:** `POST /api/permanent-tokens`

**Headers:**
```
Content-Type: application/json
<PERSON>ie: [NextAuth session cookie]
```

**Body:**
```json
{
  "name": "External API Access",
  "expiresAt": "2025-12-31T23:59:59.000Z"
}
```

**Expected Response:**
```json
{
  "message": "Token created successfully",
  "token": "64-character-hex-string",
  "tokenInfo": {
    "id": 1,
    "name": "External API Access",
    "expiresAt": "2025-12-31T23:59:59.000Z",
    "isActive": true,
    "createdAt": "2025-08-19T10:50:55.000Z"
  }
}
```

**Note:** Save the token value as it will only be shown once.

### 2. Test PDF Generation with Permanent Token

**Endpoint:** `GET /api/pdf?resume_id=1&token=YOUR_PERMANENT_TOKEN`

**Expected Response:**
- Status: 200
- Content-Type: application/pdf
- Content-Disposition: attachment; filename=resume.pdf
- Body: PDF file content

**Note:** The permanent token flow bypasses temporary token generation and passes the permanent token directly to the download page along with template information.

### 3. Test PDF Generation with NextAuth Session (Backward Compatibility)

**Endpoint:** `GET /api/pdf?resume_id=1`

**Headers:**
```
Cookie: [NextAuth session cookie]
```

**Expected Response:**
- Status: 200
- Content-Type: application/pdf
- Content-Disposition: attachment; filename=resume.pdf
- Body: PDF file content

### 4. Test Invalid Token

**Endpoint:** `GET /api/pdf?resume_id=1&token=invalid_token`

**Expected Response:**
```json
{
  "message": "Invalid or expired token"
}
```
**Status:** 401

### 5. Test Missing Authentication

**Endpoint:** `GET /api/pdf?resume_id=1`

**Headers:** (No session cookie)

**Expected Response:**
```json
{
  "message": "User not authenticated"
}
```
**Status:** 401

### 6. Test Access to Another User's Resume

**Endpoint:** `GET /api/pdf?resume_id=999&token=YOUR_PERMANENT_TOKEN`

**Expected Response:**
```json
{
  "message": "Resume not found or access denied"
}
```
**Status:** 404

### 7. List User's Permanent Tokens

**Endpoint:** `GET /api/permanent-tokens`

**Headers:**
```
Cookie: [NextAuth session cookie]
```

**Expected Response:**
```json
{
  "tokens": [
    {
      "id": 1,
      "name": "External API Access",
      "expiresAt": "2025-12-31T23:59:59.000Z",
      "isActive": true,
      "createdAt": "2025-08-19T10:50:55.000Z"
    }
  ]
}
```

### 8. Deactivate a Permanent Token

**Endpoint:** `DELETE /api/permanent-tokens?id=1&action=deactivate`

**Headers:**
```
Cookie: [NextAuth session cookie]
```

**Expected Response:**
```json
{
  "message": "Token deactivated successfully"
}
```

### 9. Test Deactivated Token

**Endpoint:** `GET /api/pdf?resume_id=1&token=DEACTIVATED_TOKEN`

**Expected Response:**
```json
{
  "message": "Invalid or expired token"
}
```
**Status:** 401

## Manual Testing Steps

1. **Setup:**
   - Log in to the application via Google OAuth
   - Create at least one resume
   - Note the resume ID from the URL or database

2. **Create Token:**
   - Use browser dev tools or Postman to call the create token endpoint
   - Save the returned token value

3. **Test Token Authentication:**
   - Use curl or Postman to test the PDF endpoint with the token
   - Verify the PDF is generated successfully

4. **Test Session Authentication:**
   - Use the same browser session to test the PDF endpoint without token
   - Verify backward compatibility

5. **Test Security:**
   - Try accessing another user's resume with your token
   - Try using an invalid token
   - Verify proper error responses

## Example curl Commands

```bash
# Create a permanent token (requires session cookie)
curl -X POST "http://localhost:8888/ai-resume/api/permanent-tokens" \
  -H "Content-Type: application/json" \
  -H "Cookie: next-auth.session-token=YOUR_SESSION_TOKEN" \
  -d '{"name": "Test Token", "expiresAt": "2025-12-31T23:59:59.000Z"}'

# Test PDF generation with permanent token
curl -X GET "http://localhost:8888/ai-resume/api/pdf?resume_id=1&token=YOUR_PERMANENT_TOKEN" \
  -o test-resume.pdf

# Test PDF generation with session (backward compatibility)
curl -X GET "http://localhost:8888/ai-resume/api/pdf?resume_id=1" \
  -H "Cookie: next-auth.session-token=YOUR_SESSION_TOKEN" \
  -o test-resume-session.pdf
```

## Expected Outcomes

- ✅ Permanent tokens can be created and managed
- ✅ PDF API accepts both token and session authentication
- ✅ Backward compatibility is maintained
- ✅ Proper access control is enforced
- ✅ Invalid tokens are rejected
- ✅ Deactivated tokens are rejected
- ✅ Users can only access their own resumes
